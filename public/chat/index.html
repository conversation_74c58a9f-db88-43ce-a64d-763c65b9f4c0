<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当贝AI聊天 - 智能对话助手</title>
    <meta name="description" content="当贝AI聊天界面，支持多模型对话、流式响应、思考模式等功能">
    <meta name="keywords" content="当贝AI,聊天,对话,人工智能,AI助手">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="css/themes.css" as="style">
    <link rel="preload" href="js/app.js" as="script">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/themes.css">
    
    <!-- 图标和主题 -->
    <link rel="icon" type="image/svg+xml" href="/logo.svg">
    <meta name="theme-color" content="#1677ff">
    
    <!-- 初始主题设置脚本 -->
    <script>
        // 防止主题闪烁，在页面加载前设置主题
        (function() {
            try {
                const savedTheme = localStorage.getItem('chat-theme') || 'light';
                document.documentElement.setAttribute('data-theme', savedTheme);
            } catch (e) {
                console.warn('无法加载保存的主题设置:', e);
                document.documentElement.setAttribute('data-theme', 'light');
            }
        })();
    </script>
</head>
<body>
    <!-- 主应用容器 -->
    <div id="app" class="app">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-left">
                <button id="sidebar-toggle" class="sidebar-toggle" aria-label="切换侧边栏">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>
                <div class="logo">
                    <svg class="logo-icon" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    <span class="logo-text">当贝AI聊天</span>
                </div>
            </div>
            <div class="header-right">
                <button id="theme-toggle" class="theme-toggle" aria-label="切换主题">
                    <svg class="icon icon-light" viewBox="0 0 24 24">
                        <path d="M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"/>
                    </svg>
                    <svg class="icon icon-dark" viewBox="0 0 24 24">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
                    </svg>
                </button>
                <button id="settings-toggle" class="settings-toggle" aria-label="设置">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="app-main">
            <!-- 侧边栏 -->
            <aside id="sidebar" class="sidebar">
                <div class="sidebar-content">
                    <!-- 新建对话按钮 -->
                    <button id="new-chat" class="new-chat-btn">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                        </svg>
                        <span>新建对话</span>
                    </button>

                    <!-- 模型选择器 -->
                    <div class="model-selector">
                        <label for="model-select" class="model-label">选择模型</label>
                        <select id="model-select" class="model-select">
                            <option value="">加载中...</option>
                        </select>
                        <div id="model-info" class="model-info"></div>
                    </div>

                    <!-- 对话选项 -->
                    <div class="chat-options">
                        <h3 class="options-title">对话选项</h3>
                        <div class="option-item" id="thinking-option">
                            <label class="option-label">
                                <input type="checkbox" id="thinking-mode" class="option-checkbox">
                                <div class="checkbox-custom"></div>
                                <div class="option-content">
                                    <div class="option-text">深度思考</div>
                                    <div class="option-desc">启用深度思考分析，提供更详细的推理过程</div>
                                </div>
                                <div class="option-status"></div>
                            </label>
                        </div>
                        <div class="option-item" id="search-option">
                            <label class="option-label">
                                <input type="checkbox" id="search-mode" class="option-checkbox">
                                <div class="checkbox-custom"></div>
                                <div class="option-content">
                                    <div class="option-text">联网搜索</div>
                                    <div class="option-desc">获取最新信息和实时数据</div>
                                </div>
                                <div class="option-status"></div>
                            </label>
                        </div>
                    </div>

                    <!-- 会话列表 -->
                    <div class="sessions-section">
                        <h3 class="sessions-title">对话历史</h3>
                        <div id="sessions-list" class="sessions-list">
                            <!-- 会话项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 聊天区域 -->
            <section class="chat-section">
                <!-- 消息显示区域 -->
                <div id="messages-container" class="messages-container">
                    <div id="messages-list" class="messages-list">
                        <!-- 欢迎消息 -->
                        <div class="welcome-message">
                            <div class="welcome-icon">
                                <svg viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h2 class="welcome-title">欢迎使用当贝AI聊天</h2>
                            <p class="welcome-desc">选择一个模型开始对话，体验智能AI助手的强大功能</p>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-section">
                    <div class="input-container">
                        <div class="input-wrapper">
                            <textarea 
                                id="message-input" 
                                class="message-input" 
                                placeholder="输入您的问题..."
                                rows="1"
                                maxlength="4000"
                            ></textarea>
                            <button id="send-button" class="send-button" disabled aria-label="发送消息">
                                <svg class="icon icon-send" viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                                <svg class="icon icon-stop" viewBox="0 0 24 24">
                                    <path d="M6 6h12v12H6z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="input-footer">
                            <div class="input-info">
                                <span id="char-count" class="char-count">0/4000</span>
                            </div>
                            <div class="input-tips">
                                <span class="tip">按 Enter 发送，Shift+Enter 换行</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 设置面板 -->
    <div id="settings-panel" class="settings-panel">
        <div class="settings-content">
            <div class="settings-header">
                <h3>设置</h3>
                <button id="settings-close" class="settings-close" aria-label="关闭设置">
                    <svg class="icon" viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="settings-body">
                <div class="setting-group">
                    <h4>外观</h4>
                    <div class="setting-item">
                        <label>主题</label>
                        <select id="theme-select">
                            <option value="light">浅色</option>
                            <option value="dark">深色</option>
                            <option value="auto">跟随系统</option>
                        </select>
                    </div>
                </div>
                <div class="setting-group">
                    <h4>对话</h4>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="auto-scroll">
                            自动滚动到最新消息
                        </label>
                    </div>
                    <div class="setting-item">
                        <label>
                            <input type="checkbox" id="sound-enabled">
                            消息提示音
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <span class="loading-text">正在处理...</span>
        </div>
    </div>

    <!-- Toast容器 -->
    <div id="toast-container" class="toast-container"></div>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/api.js"></script>
    <script src="js/markdown.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
