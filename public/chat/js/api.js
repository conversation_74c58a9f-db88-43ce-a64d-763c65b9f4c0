/**
 * 当贝AI聊天界面 - API客户端
 * 封装HTTP API调用，处理聊天和模型相关的请求
 * 实现当贝API的认证机制和正确的调用流程
 */

class ApiClient {
  constructor() {
    // 使用本地服务器API
    this.baseUrl = '/api';
    this.timeout = 30000; // 30秒超时
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）

    // 当前对话ID
    this.currentConversationId = null;

    // 当前的EventSource连接
    this.currentEventSource = null;
  }

  /**
   * 设置当前对话ID
   * @param {string} conversationId - 对话ID
   */
  setConversationId(conversationId) {
    this.currentConversationId = conversationId;
    console.log('设置对话ID:', conversationId);
  }

  /**
   * 获取当前对话ID
   * @returns {string|null} 对话ID
   */
  getConversationId() {
    return this.currentConversationId;
  }

  /**
   * 清理对话ID（新建对话时调用）
   */
  clearConversationId() {
    this.currentConversationId = null;
    console.log('清理对话ID');
  }

  /**
   * 发送HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(url, options = {}) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      const response = await fetch(`${this.baseUrl}${url}`, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text();
      }
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }

      throw error;
    }
  }

  /**
   * 带重试的请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @param {number} retries - 剩余重试次数
   * @returns {Promise} 请求结果
   */
  async requestWithRetry(url, options = {}, retries = this.maxRetries) {
    try {
      return await this.request(url, options);
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        console.warn(`请求失败，${this.retryDelay}ms后重试 (剩余${retries}次):`, error.message);
        await this.sleep(this.retryDelay);
        return this.requestWithRetry(url, options, retries - 1);
      }
      throw error;
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或服务器错误可以重试
    return error.message.includes('fetch') ||
           error.message.includes('500') ||
           error.message.includes('502') ||
           error.message.includes('503') ||
           error.message.includes('504');
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 发送流式聊天消息
   * @param {Object} params - 聊天参数
   * @param {Function} onMessage - 消息回调
   * @param {Function} onComplete - 完成回调
   * @param {Function} onError - 错误回调
   */
  async sendStreamMessage(params, onMessage, onComplete, onError) {
    const {
      messages,
      model,
      options = {}
    } = params;

    // 关闭之前的连接
    this.closeStream();

    try {
      // 规范化请求参数，兼容多种字段，避免 messages 为空 / options 键不一致
      // 1) 确保 messages 非空：若为空尝试从 question/prompt/content 构造
      const finalMessages = (Array.isArray(messages) && messages.length > 0)
        ? messages
        : (() => {
            const text = (typeof params.question === 'string' && params.question.trim())
              || (typeof params.prompt === 'string' && params.prompt.trim())
              || (typeof params.content === 'string' && params.content.trim());
            return text ? [{ role: 'user', content: String(text) }] : [];
          })();

      // 2) 统一 options 键为后端标准：deep_thinking / online_search，同时兼容 deep/online
      const finalOptions = { ...(options || {}) };
      if (finalOptions.deep === true && finalOptions.deep_thinking === undefined) finalOptions.deep_thinking = true;
      if (finalOptions.online === true && finalOptions.online_search === undefined) finalOptions.online_search = true;

      // 构建请求体
      const requestBody = {
        messages: finalMessages,
        model: model,
        stream: true,
        conversation_id: this.currentConversationId,
        options: finalOptions
      };

      console.log('发送流式聊天消息:', requestBody);

      // 发送请求
      const response = await fetch(`${this.baseUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 处理SSE流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('SSE流结束');
            break;
          }

          // 解码数据
          buffer += decoder.decode(value, { stream: true });

          // 处理完整的SSE事件
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留不完整的行

          for (const line of lines) {
            if (line.trim() === '') continue;

            // 解析SSE数据
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                console.log('SSE数据流结束');
                onComplete();
                return;
              }

              try {
                const parsed = JSON.parse(data);
                console.log('收到SSE消息:', parsed);

                // 保存conversation_id
                if (parsed.conversation_id) {
                  this.setConversationId(parsed.conversation_id);
                }

                // 处理不同类型的消息
                if (parsed.content_type === 'text') {
                  // 正常文本消息
                  onMessage({
                    type: 'text',
                    content: parsed.content,
                    messageId: parsed.id || parsed.message_id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'thinking') {
                  // 思考过程消息
                  onMessage({
                    type: 'thinking',
                    content: parsed.content,
                    messageId: parsed.id || parsed.message_id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'progress') {
                  // 进度消息（如联网搜索）
                  onMessage({
                    type: 'progress',
                    content: parsed.content,
                    messageId: parsed.id || parsed.message_id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'card') {
                  // 卡片消息（如搜索结果）
                  onMessage({
                    type: 'card',
                    content: parsed.content,
                    messageId: parsed.id || parsed.message_id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  // OpenAI格式的流式响应
                  const delta = parsed.choices[0].delta;
                  if (delta.content) {
                    onMessage({
                      type: 'text',
                      content: delta.content,
                      messageId: parsed.id,
                      conversationId: parsed.conversation_id
                    });
                  }

                  if (parsed.choices[0].finish_reason === 'stop') {
                    onComplete();
                    return;
                  }
                }
              } catch (parseError) {
                console.warn('解析SSE数据失败:', parseError, data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error('流式消息发送失败:', error);
      onError(error);
    }
  }

  /**
   * 获取模型列表（当贝API暂不支持，返回预定义模型）
   * @returns {Promise<Object>} 模型列表数据
   */
  async getModels() {
    try {
      // 优先从后端获取模型列表
      const res = await this.request('/models');
      if (res && res.success && res.data && Array.isArray(res.data.models)) {
        console.log('从后端获取模型列表:', res.data);
        return res.data;
      }
      throw new Error('后端模型接口返回格式不正确');
    } catch (error) {
      console.warn('从后端获取模型列表失败，回退到预置模型:', error.message || error);
      // 回退：使用预定义的模型列表，确保前端可用
      const models = [
        {
          id: 'doubao-1_6-thinking',
          name: '豆包思考版',
          description: '支持深度思考的智能对话模型',
          provider: '字节跳动',
          capabilities: ['chat', 'thinking', 'search'],
          maxTokens: 4000,
          isDefault: true
        },
        {
          id: 'doubao-pro',
          name: '豆包专业版',
          description: '高性能的专业对话模型',
          provider: '字节跳动',
          capabilities: ['chat', 'search'],
          maxTokens: 8000,
          isDefault: false
        }
      ];
      return {
        defaultModel: 'doubao-1_6-thinking',
        models: models,
        total: models.length
      };
    }
  }

  /**
   * 获取特定模型信息
   * @param {string} modelId - 模型ID
   * @returns {Promise<Object>} 模型信息
   */
  async getModelInfo(modelId) {
    try {
      // 从预定义模型列表中查找
      const modelsData = await this.getModels();
      const model = modelsData.models.find(m => m.id === modelId);

      if (model) {
        console.log('找到模型信息:', model);
        return model;
      } else {
        throw new Error(`模型 ${modelId} 不存在`);
      }
    } catch (error) {
      console.error('获取模型信息失败:', error);
      throw new Error(`获取模型信息失败: ${error.message}`);
    }
  }

  /**
   * 关闭当前的流式连接
   */
  closeStream() {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }

  /**
   * 关闭当前的流式连接
   */
  closeStream() {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }

  /**
   * 测试网络连接（简单的连通性测试）
   * @returns {Promise<boolean>} 连接状态
   */
  async testConnection() {
    try {
      // 尝试访问本地API
      const response = await this.request('/health');
      return true;
    } catch (error) {
      console.warn('网络连接测试失败:', error);
      return false;
    }
  }

  /**
   * 将前端选项转换为API期望的格式
   * @param {Object} options - 前端选项
   * @returns {Object} API格式的选项
   */
  transformOptionsForAPI(options) {
    const apiOptions = {};

    // 深度思考选项
    if (options.deep || options.thinking) {
      apiOptions.deep = true;
    }

    // 联网搜索选项
    if (options.online || options.search) {
      apiOptions.online = true;
    }

    return apiOptions;
  }

  /**
   * 获取错误信息的用户友好描述
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误描述
   */
  getErrorMessage(error) {
    const message = error.message || '未知错误';

    if (message.includes('fetch')) {
      return '网络连接失败，请检查网络设置';
    }

    if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请稍后重试';
    }

    if (message.includes('500')) {
      return '服务器内部错误，请稍后重试';
    }

    if (message.includes('502') || message.includes('503') || message.includes('504')) {
      return '服务暂时不可用，请稍后重试';
    }

    if (message.includes('401')) {
      return '认证失败，请检查权限';
    }

    if (message.includes('403')) {
      return '访问被拒绝，请检查权限';
    }

    if (message.includes('404')) {
      return '请求的资源不存在';
    }

    if (message.includes('429')) {
      return '请求过于频繁，请稍后重试';
    }

    return message;
  }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();
