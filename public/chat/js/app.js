/**
 * 当贝AI聊天界面 - 主应用逻辑
 * 管理整个聊天应用的状态和交互
 */

class ChatApp {
  constructor() {
    this.currentSession = null;
    this.models = [];
    this.selectedModel = null;
    this.isStreaming = false;
    this.currentStreamingMessage = null;
    
    // DOM元素引用
    this.elements = {};
    
    // 初始化应用
    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      // 获取DOM元素
      this.initElements();
      
      // 绑定事件
      this.bindEvents();
      
      // 初始化主题
      this.initTheme();
      
      // 加载模型列表
      await this.loadModels();
      
      // 加载会话
      this.loadSessions();
      
      // 检查URL参数
      this.handleUrlParams();
      
      console.log('聊天应用初始化完成');
    } catch (error) {
      console.error('应用初始化失败:', error);
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  /**
   * 获取DOM元素引用
   */
  initElements() {
    this.elements = {
      // 侧边栏
      sidebar: document.getElementById('sidebar'),
      sidebarToggle: document.getElementById('sidebar-toggle'),
      newChatBtn: document.getElementById('new-chat'),
      modelSelect: document.getElementById('model-select'),
      modelInfo: document.getElementById('model-info'),
      thinkingMode: document.getElementById('thinking-mode'),
      searchMode: document.getElementById('search-mode'),
      sessionsList: document.getElementById('sessions-list'),
      
      // 聊天区域
      messagesContainer: document.getElementById('messages-container'),
      messagesList: document.getElementById('messages-list'),
      messageInput: document.getElementById('message-input'),
      sendButton: document.getElementById('send-button'),
      charCount: document.getElementById('char-count'),
      
      // 主题和设置
      themeToggle: document.getElementById('theme-toggle'),
      settingsToggle: document.getElementById('settings-toggle'),
      settingsPanel: document.getElementById('settings-panel'),
      settingsClose: document.getElementById('settings-close'),
      themeSelect: document.getElementById('theme-select'),
      autoScroll: document.getElementById('auto-scroll'),
      soundEnabled: document.getElementById('sound-enabled'),
      
      // 加载和错误
      loadingOverlay: document.getElementById('loading-overlay'),
      errorToast: document.getElementById('error-toast')
    };
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 侧边栏切换
    this.elements.sidebarToggle?.addEventListener('click', () => {
      this.toggleSidebar();
    });

    // 新建对话
    this.elements.newChatBtn?.addEventListener('click', () => {
      this.createNewSession();
    });

    // 模型选择
    this.elements.modelSelect?.addEventListener('change', (e) => {
      this.selectModel(e.target.value);
    });

    // 消息输入
    this.elements.messageInput?.addEventListener('input', (e) => {
      this.updateCharCount();
      this.autoResizeTextarea(e.target);
    });

    this.elements.messageInput?.addEventListener('keydown', (e) => {
      this.handleInputKeydown(e);
    });

    // 发送按钮
    this.elements.sendButton?.addEventListener('click', () => {
      if (this.isStreaming) {
        this.stopStreaming();
      } else {
        this.sendMessage();
      }
    });

    // 主题切换
    this.elements.themeToggle?.addEventListener('click', () => {
      this.toggleTheme();
    });

    // 设置面板
    this.elements.settingsToggle?.addEventListener('click', () => {
      this.toggleSettings();
    });

    this.elements.settingsClose?.addEventListener('click', () => {
      this.closeSettings();
    });

    // 设置项
    this.elements.themeSelect?.addEventListener('change', (e) => {
      this.setTheme(e.target.value);
    });

    this.elements.autoScroll?.addEventListener('change', (e) => {
      this.updateSetting('autoScroll', e.target.checked);
    });

    this.elements.soundEnabled?.addEventListener('change', (e) => {
      this.updateSetting('soundEnabled', e.target.checked);
    });

    // 对话选项交互
    this.elements.thinkingMode?.addEventListener('change', (e) => {
      this.handleOptionChange('thinking', e.target.checked);
    });

    this.elements.searchMode?.addEventListener('change', (e) => {
      this.handleOptionChange('search', e.target.checked);
    });

    // 全局事件
    window.addEventListener('resize', debounce(() => {
      this.handleResize();
    }, 250));

    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // 网络状态监听
    window.addEventListener('online', () => {
      this.showToast('网络连接已恢复', 'success');
    });

    window.addEventListener('offline', () => {
      this.showToast('网络连接已断开', 'warning');
    });
  }

  /**
   * 初始化主题
   */
  initTheme() {
    const settings = storage.getSettings();
    const theme = settings.theme || 'light';
    
    // 设置主题
    document.documentElement.setAttribute('data-theme', theme);
    
    // 更新设置面板
    if (this.elements.themeSelect) {
      this.elements.themeSelect.value = theme;
    }
    
    // 更新其他设置
    if (this.elements.autoScroll) {
      this.elements.autoScroll.checked = settings.autoScroll !== false;
    }
    
    if (this.elements.soundEnabled) {
      this.elements.soundEnabled.checked = settings.soundEnabled === true;
    }
  }

  /**
   * 加载模型列表
   */
  async loadModels() {
    try {
      this.showLoading('加载模型列表...');

      const modelsData = await apiClient.getModels();
      this.models = modelsData.models || [];

      console.log('加载的模型数据:', modelsData);

      // 更新模型选择器
      this.updateModelSelect();

      // 选择默认模型
      const defaultModel = modelsData.defaultModel || (this.models.length > 0 ? this.models[0].id : null);
      if (defaultModel) {
        this.selectModel(defaultModel);
      }

      this.hideLoading();
    } catch (error) {
      this.hideLoading();
      console.error('加载模型列表失败:', error);
      this.showError('加载模型列表失败: ' + apiClient.getErrorMessage(error));

      // 如果加载失败，显示一个默认的测试模型
      this.models = [{
        id: 'test-model',
        name: '测试模型',
        description: '用于测试的默认模型',
        options: [
          { name: '深度思考', value: 'deep', enabled: true, selected: false },
          { name: '联网搜索', value: 'online', enabled: true, selected: false }
        ],
        recommended: false
      }];
      this.updateModelSelect();
      this.selectModel('test-model');
    }
  }

  /**
   * 更新模型选择器
   */
  updateModelSelect() {
    if (!this.elements.modelSelect) return;
    
    this.elements.modelSelect.innerHTML = '';
    
    if (this.models.length === 0) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = '暂无可用模型';
      option.disabled = true;
      this.elements.modelSelect.appendChild(option);
      return;
    }
    
    this.models.forEach(model => {
      const option = document.createElement('option');
      option.value = model.id;
      option.textContent = model.name;
      if (model.recommended) {
        option.textContent += ' (推荐)';
      }
      this.elements.modelSelect.appendChild(option);
    });
  }

  /**
   * 选择模型
   * @param {string} modelId - 模型ID
   */
  selectModel(modelId) {
    const model = this.models.find(m => m.id === modelId);
    if (!model) return;
    
    this.selectedModel = model;
    
    // 更新选择器
    if (this.elements.modelSelect) {
      this.elements.modelSelect.value = modelId;
    }
    
    // 更新模型信息
    this.updateModelInfo(model);
    
    // 更新选项可用性
    this.updateOptionsAvailability(model);
    
    // 更新当前会话的模型
    if (this.currentSession) {
      storage.updateSession(this.currentSession.id, { model: modelId });
      this.currentSession.model = modelId;
    }
  }

  /**
   * 更新模型信息显示
   * @param {Object} model - 模型信息
   */
  updateModelInfo(model) {
    if (!this.elements.modelInfo) return;

    let infoHtml = '';

    if (model.description) {
      infoHtml += `<div class="model-desc">${escapeHtml(model.description)}</div>`;
    }

    // 处理不同格式的选项数据
    const options = model.options || model.capabilities || [];
    if (options.length > 0) {
      let features = [];

      options.forEach(opt => {
        if (typeof opt === 'string') {
          features.push(opt);
        } else if (opt.name) {
          features.push(opt.name);
        } else if (opt.label) {
          features.push(opt.label);
        } else if (opt.key) {
          features.push(opt.key);
        }
      });

      if (features.length > 0) {
        infoHtml += `<div class="model-features">支持: ${features.join('、')}</div>`;
      }
    }

    // 显示模型标签
    if (model.badge) {
      infoHtml += `<div class="model-badge"><span class="badge badge-${model.badge.toLowerCase()}">${model.badge}</span></div>`;
    }

    if (model.recommended) {
      infoHtml += `<div class="model-recommended"><span class="badge badge-recommended">推荐</span></div>`;
    }

    this.elements.modelInfo.innerHTML = infoHtml;

    // 添加展开动画
    if (this.elements.modelInfo.innerHTML) {
      this.elements.modelInfo.classList.add('expanded');
    } else {
      this.elements.modelInfo.classList.remove('expanded');
    }
  }

  /**
   * 更新选项可用性
   * @param {Object} model - 模型信息
   */
  updateOptionsAvailability(model) {
    const options = model.options || model.capabilities || [];

    // 思考模式检测 - 支持多种格式
    let hasThinking = false;
    let thinkingSelected = false;

    // 检查不同的选项格式
    const thinkingOption = options.find(opt => {
      if (typeof opt === 'string') {
        return opt.includes('思考') || opt.includes('thinking') || opt.includes('deep');
      }
      return opt.value === 'deep' ||
             opt.name === '深度思考' ||
             opt.key === 'thinking' ||
             opt.key === 'deep_thinking' ||
             (opt.name && opt.name.includes('思考'));
    });

    if (thinkingOption) {
      hasThinking = thinkingOption.enabled !== false; // 默认启用，除非明确禁用
      thinkingSelected = thinkingOption.selected || false;
    } else {
      // 如果没有明确的选项配置，默认启用思考模式
      hasThinking = true;
      thinkingSelected = false;
    }

    const thinkingContainer = document.getElementById('thinking-option');
    if (this.elements.thinkingMode && thinkingContainer) {
      this.elements.thinkingMode.disabled = !hasThinking;
      this.elements.thinkingMode.checked = thinkingSelected;

      // 更新容器样式
      if (hasThinking) {
        thinkingContainer.classList.remove('disabled');
      } else {
        thinkingContainer.classList.add('disabled');
      }
    }

    // 联网搜索检测 - 支持多种格式
    let hasSearch = false;
    let searchSelected = false;

    const searchOption = options.find(opt => {
      if (typeof opt === 'string') {
        return opt.includes('搜索') || opt.includes('search') || opt.includes('online');
      }
      return opt.value === 'online' ||
             opt.name === '联网搜索' ||
             opt.key === 'search' ||
             opt.key === 'online_search' ||
             (opt.name && opt.name.includes('搜索'));
    });

    if (searchOption) {
      hasSearch = searchOption.enabled !== false; // 默认启用，除非明确禁用
      searchSelected = searchOption.selected || false;
    } else {
      // 如果没有明确的选项配置，默认启用搜索功能
      hasSearch = true;
      searchSelected = false;
    }

    const searchContainer = document.getElementById('search-option');
    if (this.elements.searchMode && searchContainer) {
      this.elements.searchMode.disabled = !hasSearch;
      this.elements.searchMode.checked = searchSelected;

      // 更新容器样式
      if (hasSearch) {
        searchContainer.classList.remove('disabled');
      } else {
        searchContainer.classList.add('disabled');
      }
    }

    console.log('选项可用性更新:', {
      model: model.name || model.id,
      thinking: { available: hasThinking, selected: thinkingSelected },
      search: { available: hasSearch, selected: searchSelected },
      options: options
    });
  }

  /**
   * 加载会话列表
   */
  loadSessions() {
    const sessions = storage.getSessions();
    this.updateSessionsList(sessions);
    
    // 加载当前会话
    const currentSession = storage.getCurrentSession();
    if (currentSession) {
      this.loadSession(currentSession.id);
    } else if (sessions.length === 0) {
      // 如果没有会话，创建一个新的
      this.createNewSession();
    }
  }

  /**
   * 更新会话列表显示
   * @param {Array} sessions - 会话列表
   */
  updateSessionsList(sessions) {
    if (!this.elements.sessionsList) return;
    
    this.elements.sessionsList.innerHTML = '';
    
    if (sessions.length === 0) {
      const emptyDiv = document.createElement('div');
      emptyDiv.className = 'sessions-empty';
      emptyDiv.textContent = '暂无对话历史';
      this.elements.sessionsList.appendChild(emptyDiv);
      return;
    }
    
    sessions.forEach(session => {
      const sessionElement = this.createSessionElement(session);
      this.elements.sessionsList.appendChild(sessionElement);
    });
  }

  /**
   * 创建会话元素
   * @param {Object} session - 会话对象
   * @returns {HTMLElement} 会话元素
   */
  createSessionElement(session) {
    const div = document.createElement('div');
    div.className = 'session-item';
    div.dataset.sessionId = session.id;
    
    if (this.currentSession && this.currentSession.id === session.id) {
      div.classList.add('active');
    }
    
    const lastMessage = session.messages[session.messages.length - 1];
    const preview = lastMessage ? 
      (lastMessage.role === 'user' ? lastMessage.content : '回复: ' + lastMessage.content) : 
      '新对话';
    
    div.innerHTML = `
      <svg class="session-icon" viewBox="0 0 24 24">
        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
      </svg>
      <div class="session-content">
        <div class="session-title">${escapeHtml(session.title)}</div>
        <div class="session-preview">${escapeHtml(preview.substring(0, 30))}${preview.length > 30 ? '...' : ''}</div>
      </div>
      <div class="session-actions">
        <button class="session-action" onclick="chatApp.deleteSession('${session.id}')" title="删除对话">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </button>
      </div>
    `;
    
    // 点击切换会话
    div.addEventListener('click', (e) => {
      if (!e.target.closest('.session-actions')) {
        this.loadSession(session.id);
      }
    });
    
    return div;
  }

  /**
   * 添加消息到界面
   * @param {Object} message - 消息对象
   * @returns {HTMLElement} 消息元素
   */
  addMessage(message) {
    if (!this.elements.messagesList) return null;

    // 移除欢迎消息
    const welcomeMessage = this.elements.messagesList.querySelector('.welcome-message');
    if (welcomeMessage) {
      welcomeMessage.remove();
    }

    const messageElement = this.createMessageElement(message);
    this.elements.messagesList.appendChild(messageElement);

    // 自动滚动
    this.scrollToBottom();

    return messageElement;
  }

  /**
   * 创建消息元素
   * @param {Object} message - 消息对象
   * @returns {HTMLElement} 消息元素
   */
  createMessageElement(message) {
    const div = document.createElement('div');
    div.className = `message ${message.role}`;

    if (message.streaming) {
      div.classList.add('streaming');
    }

    const avatar = message.role === 'user' ? '用' : 'AI';
    const content = message.content || '';
    const renderedContent = message.role === 'assistant' ?
      markdownRenderer.render(content) :
      escapeHtml(content);

    div.innerHTML = `
      <div class="message-avatar">${avatar}</div>
      <div class="message-content">
        <div class="message-bubble" ${message.role === 'assistant' ? `data-raw-content="${escapeHtml(content)}"` : ''}>
          ${renderedContent}
          ${message.streaming ? '<div class="typing-indicator"><div class="typing-dots"><div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div></div></div>' : ''}
        </div>
        <div class="message-time">${message.timestamp ? formatTime(message.timestamp) : ''}</div>
        ${message.role === 'assistant' ? `
          <div class="message-actions">
            <button class="message-action" onclick="copyMessage(this)" title="复制消息">
              <svg class="icon" viewBox="0 0 24 24">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
              </svg>
            </button>
          </div>
        ` : ''}
      </div>
    `;

    return div;
  }

  /**
   * 加载消息列表
   * @param {Array} messages - 消息列表
   */
  loadMessages(messages) {
    this.clearMessages();

    if (messages.length === 0) {
      this.showWelcomeMessage();
      return;
    }

    messages.forEach(message => {
      this.addMessage(message);
    });
  }

  /**
   * 清空消息列表
   */
  clearMessages() {
    if (this.elements.messagesList) {
      this.elements.messagesList.innerHTML = '';
    }
  }

  /**
   * 显示欢迎消息
   */
  showWelcomeMessage() {
    if (!this.elements.messagesList) return;

    const welcomeDiv = document.createElement('div');
    welcomeDiv.className = 'welcome-message';
    welcomeDiv.innerHTML = `
      <div class="welcome-icon">
        <svg viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
      <h2 class="welcome-title">欢迎使用当贝AI聊天</h2>
      <p class="welcome-desc">选择一个模型开始对话，体验智能AI助手的强大功能</p>
    `;

    this.elements.messagesList.appendChild(welcomeDiv);
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    const settings = storage.getSettings();
    if (settings.autoScroll !== false) {
      if (this.elements.messagesContainer) {
        this.elements.messagesContainer.scrollTop = this.elements.messagesContainer.scrollHeight;
      }
    }
  }

  /**
   * 更新会话列表状态
   */
  updateSessionsListState() {
    const sessionItems = document.querySelectorAll('.session-item');
    sessionItems.forEach(item => {
      item.classList.remove('active');
      if (this.currentSession && item.dataset.sessionId === this.currentSession.id) {
        item.classList.add('active');
      }
    });
  }

  /**
   * 切换侧边栏
   */
  toggleSidebar() {
    if (this.elements.sidebar) {
      this.elements.sidebar.classList.toggle('open');
    }
  }

  /**
   * 切换主题
   */
  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称
   */
  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);

    // 保存设置
    this.updateSetting('theme', theme);

    // 更新设置面板
    if (this.elements.themeSelect) {
      this.elements.themeSelect.value = theme;
    }
  }

  /**
   * 切换设置面板
   */
  toggleSettings() {
    if (this.elements.settingsPanel) {
      this.elements.settingsPanel.classList.toggle('open');
    }
  }

  /**
   * 关闭设置面板
   */
  closeSettings() {
    if (this.elements.settingsPanel) {
      this.elements.settingsPanel.classList.remove('open');
    }
  }

  /**
   * 更新设置
   * @param {string} key - 设置键
   * @param {any} value - 设置值
   */
  updateSetting(key, value) {
    const settings = storage.getSettings();
    settings[key] = value;
    storage.setSettings(settings);
  }

  /**
   * 处理选项变化
   * @param {string} optionType - 选项类型
   * @param {boolean} checked - 是否选中
   */
  handleOptionChange(optionType, checked) {
    // 添加激活动画
    const optionId = optionType === 'thinking' ? 'thinking-option' : 'search-option';
    const optionElement = document.getElementById(optionId);

    if (optionElement) {
      optionElement.classList.add('activating');
      setTimeout(() => {
        optionElement.classList.remove('activating');
      }, 300);
    }

    // 显示状态提示
    const optionName = optionType === 'thinking' ? '深度思考' : '联网搜索';
    const statusText = checked ? '已启用' : '已禁用';

    // 可以在这里添加更多的交互逻辑
    console.log(`${optionName}${statusText}`);
  }

  /**
   * 处理输入框键盘事件
   * @param {KeyboardEvent} e - 键盘事件
   */
  handleInputKeydown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      this.sendMessage();
    }
  }

  /**
   * 自动调整文本框高度
   * @param {HTMLTextAreaElement} textarea - 文本框元素
   */
  autoResizeTextarea(textarea) {
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';

    // 计算新高度，考虑行高和内边距
    const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight) || 24;
    const minHeight = lineHeight + 4; // 最小高度：一行 + 内边距
    const maxHeight = 120; // 最大高度

    // 设置新高度
    const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight));
    textarea.style.height = newHeight + 'px';

    // 如果内容超过最大高度，显示滚动条
    if (textarea.scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  }

  /**
   * 更新字符计数
   */
  updateCharCount() {
    if (this.elements.messageInput && this.elements.charCount) {
      const length = this.elements.messageInput.value.length;
      const maxLength = this.elements.messageInput.maxLength || 4000;
      this.elements.charCount.textContent = `${length}/${maxLength}`;

      // 更新发送按钮状态
      if (this.elements.sendButton) {
        this.elements.sendButton.disabled = length === 0 || this.isStreaming;
      }
    }
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 移动端自动关闭侧边栏
    if (getDeviceType() === 'mobile' && this.elements.sidebar) {
      this.elements.sidebar.classList.remove('open');
    }
  }

  /**
   * 处理URL参数
   */
  handleUrlParams() {
    const sessionId = getUrlParam('session');
    if (sessionId) {
      this.loadSession(sessionId);
    }
  }

  /**
   * 显示加载状态
   * @param {string} message - 加载消息
   */
  showLoading(message = '加载中...') {
    if (this.elements.loadingOverlay) {
      const loadingText = this.elements.loadingOverlay.querySelector('.loading-text');
      if (loadingText) {
        loadingText.textContent = message;
      }
      this.elements.loadingOverlay.classList.add('show');
    }
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    if (this.elements.loadingOverlay) {
      this.elements.loadingOverlay.classList.remove('show');
    }
  }

  /**
   * 显示错误消息
   * @param {string} message - 错误消息
   */
  showError(message) {
    showToast(message, 'error', 5000);
  }

  /**
   * 显示成功消息
   * @param {string} message - 成功消息
   */
  showSuccess(message) {
    showToast(message, 'success', 3000);
  }

  /**
   * 显示提示消息
   * @param {string} message - 提示消息
   */
  showToast(message, type = 'info', duration = 3000) {
    showToast(message, type, duration);
  }

  /**
   * 创建新会话
   */
  createNewSession() {
    const model = this.selectedModel ? this.selectedModel.id : '';
    const session = storage.createSession('新对话', model);

    this.currentSession = session;

    // 清理旧的conversation_id
    apiClient.clearConversationId();

    this.loadSessions(); // 刷新会话列表
    this.clearMessages();
    this.showWelcomeMessage();

    // 更新URL
    setUrlParam('session', session.id);

    console.log('创建新会话，已清理conversation_id:', session);
  }

  /**
   * 加载会话
   * @param {string} sessionId - 会话ID
   */
  loadSession(sessionId) {
    const sessions = storage.getSessions();
    const session = sessions.find(s => s.id === sessionId);

    if (!session) {
      this.showError('会话不存在');
      return;
    }

    this.currentSession = session;
    storage.setCurrentSession(sessionId);

    // 设置conversation_id到API客户端
    if (session.conversationId) {
      apiClient.setConversationId(session.conversationId);
      console.log('恢复conversation_id:', session.conversationId);
    } else {
      apiClient.clearConversationId();
    }

    // 更新模型选择
    if (session.model && session.model !== this.selectedModel?.id) {
      this.selectModel(session.model);
    }

    // 加载消息
    this.loadMessages(session.messages);

    // 更新会话列表状态
    this.updateSessionsListState();

    // 更新URL
    setUrlParam('session', sessionId);
  }

  /**
   * 删除会话
   * @param {string} sessionId - 会话ID
   */
  deleteSession(sessionId) {
    if (confirm('确定要删除这个对话吗？')) {
      storage.deleteSession(sessionId);

      // 如果删除的是当前会话，切换到其他会话或创建新会话
      if (this.currentSession && this.currentSession.id === sessionId) {
        const sessions = storage.getSessions();
        if (sessions.length > 0) {
          this.loadSession(sessions[0].id);
        } else {
          this.createNewSession();
        }
      }

      this.loadSessions(); // 刷新会话列表
      this.showSuccess('对话已删除');
    }
  }

  /**
   * 发送消息
   */
  async sendMessage() {
    const input = this.elements.messageInput;
    if (!input) return;

    const content = input.value.trim();
    if (!content) return;

    if (!this.selectedModel) {
      this.showError('请先选择一个模型');
      return;
    }

    if (!this.currentSession) {
      this.createNewSession();
    }

    // 清空输入框
    input.value = '';
    this.updateCharCount();
    this.autoResizeTextarea(input);

    // 添加用户消息
    const userMessage = {
      role: 'user',
      content: content
    };

    this.addMessage(userMessage);
    storage.addMessage(this.currentSession.id, userMessage);

    // 准备发送请求
    const messages = this.currentSession.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));

    // 获取选项
    const options = this.getSelectedOptions();

    try {
      this.startStreaming();

      // 构建消息历史
      const messages = this.currentSession.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // 创建AI消息占位符
      const aiMessage = {
        role: 'assistant',
        content: '',
        streaming: true
      };

      this.currentStreamingMessage = this.addMessage(aiMessage);

      // 发送流式请求
      await apiClient.sendStreamMessage({
        messages: messages,
        model: this.selectedModel.id,
        options: options
      },
      (message) => {
        this.handleStreamMessage(message);
      },
      () => {
        this.handleStreamComplete();
      },
      (error) => {
        this.handleStreamError(error);
      });

    } catch (error) {
      this.handleStreamError(error);
    }
  }

  /**
   * 获取选中的选项
   * @returns {Object} 选项对象
   */
  getSelectedOptions() {
    const options = {};

    // 标准化为后端类型定义：deep_thinking / online_search
    if (this.elements.thinkingMode && this.elements.thinkingMode.checked && !this.elements.thinkingMode.disabled) {
      options.deep_thinking = true; // 标准键：深度思考
    }

    if (this.elements.searchMode && this.elements.searchMode.checked && !this.elements.searchMode.disabled) {
      options.online_search = true; // 标准键：联网搜索
    }

    return options;
  }

  /**
   * 开始流式响应
   */
  startStreaming() {
    this.isStreaming = true;

    if (this.elements.sendButton) {
      this.elements.sendButton.classList.add('sending');
      this.elements.sendButton.title = '停止生成';
    }

    if (this.elements.messageInput) {
      this.elements.messageInput.disabled = true;
    }
  }

  /**
   * 停止流式响应
   */
  stopStreaming() {
    this.isStreaming = false;
    apiClient.closeStream();

    if (this.elements.sendButton) {
      this.elements.sendButton.classList.remove('sending');
      this.elements.sendButton.title = '发送消息';
    }

    if (this.elements.messageInput) {
      this.elements.messageInput.disabled = false;
      this.elements.messageInput.focus();
    }

    // 完成当前流式消息
    if (this.currentStreamingMessage) {
      this.completeStreamingMessage();
    }
  }

  /**
   * 处理流式消息
   * @param {Object} message - 消息对象
   */
  handleStreamMessage(message) {
    if (!this.currentStreamingMessage) return;

    const messageElement = this.currentStreamingMessage;
    const contentElement = messageElement.querySelector('.message-bubble');

    if (!contentElement) return;

    // 根据消息类型处理
    switch (message.type) {
      case 'text':
        // 正常文本消息
        const currentContent = contentElement.dataset.rawContent || '';
        const newContent = currentContent + message.content;
        contentElement.dataset.rawContent = newContent;
        contentElement.innerHTML = markdownRenderer.render(newContent);
        break;

      case 'thinking':
        // 思考过程消息 - 可以选择显示或隐藏
        if (this.elements.thinkingMode && this.elements.thinkingMode.checked) {
          const thinkingContent = contentElement.dataset.thinkingContent || '';
          const newThinkingContent = thinkingContent + message.content;
          contentElement.dataset.thinkingContent = newThinkingContent;

          // 显示思考过程（可以用不同的样式）
          const mainContent = contentElement.dataset.rawContent || '';
          const displayContent = mainContent + (newThinkingContent ? `\n\n**思考过程：**\n${newThinkingContent}` : '');
          contentElement.innerHTML = markdownRenderer.render(displayContent);
        }
        break;

      case 'progress':
        // 进度消息（如联网搜索）
        const progressHtml = `<div class="progress-message">
          <div class="progress-icon">
            <svg class="icon spinning" viewBox="0 0 24 24">
              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
            </svg>
          </div>
          <span>${message.content}</span>
        </div>`;

        // 临时显示进度，不累积到最终内容
        contentElement.innerHTML = progressHtml;
        break;

      case 'card':
        // 卡片消息（如搜索结果）
        try {
          const cardData = JSON.parse(message.content);
          const cardHtml = this.renderCard(cardData);

          // 将卡片内容添加到消息中
          const currentContent = contentElement.dataset.rawContent || '';
          const newContent = currentContent + '\n\n' + cardHtml;
          contentElement.dataset.rawContent = newContent;
          contentElement.innerHTML = markdownRenderer.render(newContent);
        } catch (error) {
          console.warn('解析卡片数据失败:', error);
        }
        break;

      default:
        console.warn('未知的消息类型:', message.type);
    }

    // 更新对话ID
    if (message.conversationId && this.currentSession) {
      this.currentSession.conversationId = message.conversationId;
      // 同时更新API客户端中的对话ID
      apiClient.setConversationId(message.conversationId);
      storage.updateSession(this.currentSession.id, {
        conversationId: message.conversationId
      });
    }

    // 自动滚动
    this.scrollToBottom();
  }

  /**
   * 渲染卡片内容
   * @param {Object} cardData - 卡片数据
   * @returns {string} 渲染后的HTML
   */
  renderCard(cardData) {
    if (cardData.cardType === 'DB-CARD-2' && cardData.cardInfo) {
      const cardInfo = cardData.cardInfo;
      let html = `### ${cardInfo.title || cardInfo.initTitle}\n\n`;

      if (cardInfo.cardItems) {
        cardInfo.cardItems.forEach(item => {
          if (item.type === '2001') {
            // 搜索网页
            html += `**${item.name}：**\n`;
            try {
              const searchTerms = JSON.parse(item.content);
              html += searchTerms.map(term => `- ${term}`).join('\n') + '\n\n';
            } catch (e) {
              html += `${item.content}\n\n`;
            }
          } else if (item.type === '2002') {
            // 引用分析
            html += `**${item.name}：**\n`;
            try {
              const references = JSON.parse(item.content);
              references.forEach((ref, index) => {
                html += `${index + 1}. [${ref.name}](${ref.url})\n   ${ref.snippet}\n\n`;
              });
            } catch (e) {
              html += `${item.content}\n\n`;
            }
          }
        });
      }

      return html;
    }

    return JSON.stringify(cardData, null, 2);
  }

  /**
   * 处理流式完成
   * @param {Object} data - 完成数据
   */
  handleStreamComplete(data) {
    this.completeStreamingMessage();
    this.stopStreaming();
  }

  /**
   * 处理流式错误
   * @param {Error} error - 错误对象
   */
  handleStreamError(error) {
    console.error('流式响应错误:', error);

    if (this.currentStreamingMessage) {
      const messageElement = this.currentStreamingMessage;
      const contentElement = messageElement.querySelector('.message-bubble');

      if (contentElement) {
        contentElement.innerHTML = `<div class="error-message">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          <span>消息发送失败: ${apiClient.getErrorMessage(error)}</span>
        </div>`;
      }
    }

    this.stopStreaming();
    this.showError('消息发送失败: ' + apiClient.getErrorMessage(error));
  }

  /**
   * 完成流式消息
   */
  completeStreamingMessage() {
    if (this.currentStreamingMessage) {
      const messageElement = this.currentStreamingMessage;
      const contentElement = messageElement.querySelector('.message-bubble');

      if (contentElement) {
        const content = contentElement.dataset.rawContent || '';

        // 保存到存储
        const aiMessage = {
          role: 'assistant',
          content: content
        };

        storage.addMessage(this.currentSession.id, aiMessage);

        // 更新消息状态
        messageElement.classList.remove('streaming');

        // 添加时间戳
        const timeElement = messageElement.querySelector('.message-time');
        if (timeElement) {
          timeElement.textContent = formatTime(Date.now());
        }
      }

      this.currentStreamingMessage = null;
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 关闭流式连接
    if (this.isStreaming) {
      apiClient.closeStream();
    }
  }
}

// 创建全局应用实例
let chatApp;

/**
 * 复制消息内容
 * @param {HTMLElement} button - 复制按钮
 */
function copyMessage(button) {
  const messageElement = button.closest('.message');
  const contentElement = messageElement.querySelector('.message-bubble');

  if (contentElement) {
    const rawContent = contentElement.dataset.rawContent || contentElement.textContent;
    copyToClipboard(rawContent).then(success => {
      if (success) {
        showToast('消息已复制到剪贴板', 'success', 2000);
      } else {
        showToast('复制失败，请手动选择复制', 'error', 3000);
      }
    });
  }
}

/**
 * 重新生成回复
 * @param {HTMLElement} button - 重新生成按钮
 */
function regenerateMessage(button) {
  const messageElement = button.closest('.message');
  const messageIndex = Array.from(messageElement.parentNode.children).indexOf(messageElement);

  if (chatApp && chatApp.currentSession) {
    // 移除当前消息及之后的所有消息
    const messages = chatApp.currentSession.messages;
    chatApp.currentSession.messages = messages.slice(0, messageIndex);

    // 重新发送最后一条用户消息
    const lastUserMessage = messages.slice(0, messageIndex).reverse().find(msg => msg.role === 'user');
    if (lastUserMessage) {
      chatApp.elements.messageInput.value = lastUserMessage.content;
      chatApp.sendMessage();
    }
  }
}

/**
 * 导出对话
 */
function exportChat() {
  if (!chatApp || !chatApp.currentSession) {
    showToast('没有可导出的对话', 'warning');
    return;
  }

  const session = chatApp.currentSession;
  const exportData = {
    title: session.title,
    model: session.model,
    messages: session.messages,
    createdAt: session.createdAt,
    exportedAt: Date.now()
  };

  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = `${session.title}_${formatTime(Date.now(), false)}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showToast('对话已导出', 'success');
}

/**
 * 导入对话
 */
function importChat() {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';

  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);

        // 验证数据格式
        if (!data.messages || !Array.isArray(data.messages)) {
          throw new Error('无效的对话文件格式');
        }

        // 创建新会话
        const session = storage.createSession(data.title || '导入的对话', data.model || '');

        // 添加消息
        data.messages.forEach(message => {
          storage.addMessage(session.id, message);
        });

        // 加载会话
        if (chatApp) {
          chatApp.loadSession(session.id);
          chatApp.loadSessions();
        }

        showToast('对话导入成功', 'success');
      } catch (error) {
        console.error('导入对话失败:', error);
        showToast('导入失败: ' + error.message, 'error');
      }
    };

    reader.readAsText(file);
  };

  input.click();
}

/**
 * 清空所有对话
 */
function clearAllChats() {
  if (confirm('确定要清空所有对话吗？此操作不可恢复。')) {
    storage.clearData();

    if (chatApp) {
      chatApp.createNewSession();
      chatApp.loadSessions();
    }

    showToast('所有对话已清空', 'success');
  }
}

/**
 * 显示快捷键帮助
 */
function showKeyboardShortcuts() {
  const shortcuts = [
    { key: 'Enter', desc: '发送消息' },
    { key: 'Shift + Enter', desc: '换行' },
    { key: 'Ctrl + N', desc: '新建对话' },
    { key: 'Ctrl + /', desc: '显示快捷键' },
    { key: 'Ctrl + D', desc: '切换深色模式' },
    { key: 'Esc', desc: '停止生成' }
  ];

  const shortcutsHtml = shortcuts.map(s =>
    `<div class="shortcut-item">
      <kbd>${s.key}</kbd>
      <span>${s.desc}</span>
    </div>`
  ).join('');

  // 这里可以显示一个模态框或提示
  showToast('快捷键帮助已显示在控制台', 'info');
  console.table(shortcuts);
}

// 全局键盘快捷键
document.addEventListener('keydown', (e) => {
  // Ctrl + N: 新建对话
  if (e.ctrlKey && e.key === 'n') {
    e.preventDefault();
    if (chatApp) {
      chatApp.createNewSession();
    }
  }

  // Ctrl + D: 切换深色模式
  if (e.ctrlKey && e.key === 'd') {
    e.preventDefault();
    if (chatApp) {
      chatApp.toggleTheme();
    }
  }

  // Ctrl + /: 显示快捷键
  if (e.ctrlKey && e.key === '/') {
    e.preventDefault();
    showKeyboardShortcuts();
  }

  // Esc: 停止生成
  if (e.key === 'Escape') {
    if (chatApp && chatApp.isStreaming) {
      chatApp.stopStreaming();
    }
  }
});

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
  chatApp = new ChatApp();
});
